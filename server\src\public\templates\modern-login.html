<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASH MIS - Login</title>
    <link rel="stylesheet" href="../styles/wash-mis-design-system.css">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💧</text></svg>">
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-4);
        }
        
        .login-card {
            background: white;
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--primary-50) 0%, white 100%);
            padding: var(--space-8) var(--space-6) var(--space-6);
            text-align: center;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .login-body {
            padding: var(--space-8) var(--space-6);
        }
        
        .login-footer {
            background-color: var(--gray-50);
            padding: var(--space-6);
            text-align: center;
            border-top: 1px solid var(--gray-200);
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: var(--space-4);
        }
        
        .system-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            color: var(--gray-900);
            margin-bottom: var(--space-2);
        }
        
        .system-subtitle {
            font-size: var(--text-sm);
            color: var(--gray-600);
            margin-bottom: var(--space-1);
        }
        
        .ministry-name {
            font-size: var(--text-xs);
            color: var(--gray-500);
            font-weight: var(--font-medium);
        }
        
        .form-group {
            margin-bottom: var(--space-6);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .forgot-password {
            text-align: right;
            margin-top: var(--space-2);
        }
        
        .forgot-password a {
            color: var(--primary-600);
            text-decoration: none;
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
        }
        
        .forgot-password a:hover {
            text-decoration: underline;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: var(--space-6) 0;
        }
        
        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: var(--gray-300);
        }
        
        .divider span {
            padding: 0 var(--space-4);
            color: var(--gray-500);
            font-size: var(--text-sm);
        }
        
        .security-notice {
            background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
            border: 1px solid var(--primary-200);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-top: var(--space-6);
        }
        
        .security-notice p {
            font-size: var(--text-xs);
            color: var(--primary-700);
            margin: 0;
            line-height: 1.5;
        }
        
        .btn-login {
            width: 100%;
            padding: var(--space-4);
            font-size: var(--text-base);
            font-weight: var(--font-semibold);
            border-radius: var(--radius-xl);
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            color: white;
            border: none;
            cursor: pointer;
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-md);
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 100%);
            box-shadow: var(--shadow-lg);
            transform: translateY(-1px);
        }
        
        .btn-login:focus {
            outline: 2px solid var(--primary-500);
            outline-offset: 2px;
        }
        
        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-icon {
            position: absolute;
            left: var(--space-3);
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: var(--text-lg);
        }
        
        .form-input-with-icon {
            padding-left: calc(var(--space-10) + var(--space-2));
        }
        
        .error-message {
            color: var(--error-600);
            font-size: var(--text-sm);
            margin-top: var(--space-2);
            display: none;
        }
        
        .form-input.error {
            border-color: var(--error-500);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
        
        .form-input.error + .error-message {
            display: block;
        }
        
        @media (max-width: 480px) {
            .login-container {
                padding: var(--space-2);
            }
            
            .login-header,
            .login-body,
            .login-footer {
                padding: var(--space-6) var(--space-4);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Header -->
            <div class="login-header">
                <div class="logo">💧</div>
                <h1 class="system-title">WASH MIS</h1>
                <p class="system-subtitle">Water, Sanitation, and Hygiene</p>
                <p class="system-subtitle">Management Information System</p>
                <p class="ministry-name">Ministry of Infrastructure - Rwanda</p>
            </div>
            
            <!-- Login Form -->
            <div class="login-body">
                <form id="loginForm">
                    <div class="form-group">
                        <label class="form-label" for="email">Email Address</label>
                        <div class="input-group">
                            <span class="input-icon">📧</span>
                            <input 
                                type="email" 
                                id="email" 
                                name="email"
                                class="form-input form-input-with-icon" 
                                placeholder="Enter your email"
                                required
                                autocomplete="email"
                            >
                        </div>
                        <div class="error-message">Please enter a valid email address</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="password">Password</label>
                        <div class="input-group">
                            <span class="input-icon">🔒</span>
                            <input 
                                type="password" 
                                id="password" 
                                name="password"
                                class="form-input form-input-with-icon" 
                                placeholder="Enter your password"
                                required
                                autocomplete="current-password"
                            >
                        </div>
                        <div class="error-message">Password is required</div>
                        <div class="forgot-password">
                            <a href="#" id="forgotPasswordLink">Forgot your password?</a>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn-login" id="loginButton">
                            Sign In
                        </button>
                    </div>
                </form>
                
                <div class="divider">
                    <span>Need help?</span>
                </div>
                
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Contact your system administrator for account access
                    </p>
                </div>
                
                <div class="security-notice">
                    <p>
                        🔐 <strong>Security Notice:</strong> This system uses two-factor authentication. 
                        You may be prompted for an additional verification code after entering your credentials.
                    </p>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="login-footer">
                <p class="text-xs text-gray-500">
                    © 2024 Ministry of Infrastructure, Rwanda
                </p>
                <p class="text-xs text-gray-400 mt-1">
                    Secure access to WASH infrastructure data
                </p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const loginButton = document.getElementById('loginButton');
            const forgotPasswordLink = document.getElementById('forgotPasswordLink');

            // Form validation
            function validateEmail(email) {
                const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return re.test(email);
            }

            function showError(input, show = true) {
                if (show) {
                    input.classList.add('error');
                } else {
                    input.classList.remove('error');
                }
            }

            // Real-time validation
            emailInput.addEventListener('blur', function() {
                const isValid = validateEmail(this.value);
                showError(this, !isValid && this.value);
            });

            passwordInput.addEventListener('blur', function() {
                showError(this, !this.value);
            });

            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = emailInput.value;
                const password = passwordInput.value;
                
                // Validate inputs
                const emailValid = validateEmail(email);
                const passwordValid = password.length > 0;
                
                showError(emailInput, !emailValid);
                showError(passwordInput, !passwordValid);
                
                if (emailValid && passwordValid) {
                    // Show loading state
                    loginButton.disabled = true;
                    loginButton.innerHTML = 'Signing In...';
                    loginButton.classList.add('loading');
                    
                    // Simulate API call
                    setTimeout(() => {
                        // Reset button state
                        loginButton.disabled = false;
                        loginButton.innerHTML = 'Sign In';
                        loginButton.classList.remove('loading');
                        
                        // In a real app, this would redirect or show 2FA prompt
                        alert('Login successful! (This is a demo)');
                    }, 2000);
                }
            });

            // Forgot password link
            forgotPasswordLink.addEventListener('click', function(e) {
                e.preventDefault();
                alert('Password reset functionality would be implemented here.');
            });

            // Add some visual feedback
            [emailInput, passwordInput].forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                    this.parentElement.style.transition = 'transform 0.2s ease';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
