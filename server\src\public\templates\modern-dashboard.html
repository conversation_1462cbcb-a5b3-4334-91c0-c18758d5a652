<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASH MIS - Dashboard</title>
    <link rel="stylesheet" href="../styles/wash-mis-design-system.css">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💧</text></svg>">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container flex justify-between items-center">
            <a href="#" class="navbar-brand">
                💧 WASH MIS
            </a>
            <div class="navbar-nav">
                <a href="#" class="nav-link">Dashboard</a>
                <a href="#" class="nav-link">Water Systems</a>
                <a href="#" class="nav-link">Sanitation</a>
                <a href="#" class="nav-link">Hygiene</a>
                <a href="#" class="nav-link">Reports</a>
                <a href="#" class="nav-link">Settings</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-8">
        <!-- Header Section -->
        <header class="mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">
                Water, Sanitation, and Hygiene Dashboard
            </h1>
            <p class="text-lg text-gray-600">
                Ministry of Infrastructure - Republic of Rwanda
            </p>
        </header>

        <!-- Stats Grid -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Water Access</p>
                            <p class="text-3xl font-bold text-primary">87.5%</p>
                        </div>
                        <div class="text-4xl">🚰</div>
                    </div>
                    <div class="mt-4">
                        <span class="text-sm text-green-600">↗ +2.3% from last month</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Sanitation Coverage</p>
                            <p class="text-3xl font-bold text-primary">72.1%</p>
                        </div>
                        <div class="text-4xl">🚽</div>
                    </div>
                    <div class="mt-4">
                        <span class="text-sm text-green-600">↗ +1.8% from last month</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Hygiene Facilities</p>
                            <p class="text-3xl font-bold text-primary">65.3%</p>
                        </div>
                        <div class="text-4xl">🧼</div>
                    </div>
                    <div class="mt-4">
                        <span class="text-sm text-yellow-600">→ +0.5% from last month</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Active Projects</p>
                            <p class="text-3xl font-bold text-primary">142</p>
                        </div>
                        <div class="text-4xl">🏗️</div>
                    </div>
                    <div class="mt-4">
                        <span class="text-sm text-green-600">↗ +12 new projects</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Content Grid -->
        <section class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Recent Activities -->
            <div class="lg:col-span-2">
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-xl font-semibold text-gray-900">Recent Activities</h2>
                    </div>
                    <div class="card-body">
                        <div class="space-y-4">
                            <div class="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
                                <div class="text-2xl">💧</div>
                                <div class="flex-1">
                                    <h3 class="font-medium text-gray-900">New Water System Installed</h3>
                                    <p class="text-sm text-gray-600">Kigali District - Gasabo Sector</p>
                                    <p class="text-xs text-gray-500 mt-1">2 hours ago</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
                                <div class="text-2xl">🚽</div>
                                <div class="flex-1">
                                    <h3 class="font-medium text-gray-900">Sanitation Facility Upgraded</h3>
                                    <p class="text-sm text-gray-600">Southern Province - Huye District</p>
                                    <p class="text-xs text-gray-500 mt-1">5 hours ago</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
                                <div class="text-2xl">📊</div>
                                <div class="flex-1">
                                    <h3 class="font-medium text-gray-900">Monthly Report Generated</h3>
                                    <p class="text-sm text-gray-600">National WASH Coverage Report</p>
                                    <p class="text-xs text-gray-500 mt-1">1 day ago</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div>
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-xl font-semibold text-gray-900">Quick Actions</h2>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button class="btn btn-primary w-full">
                                Add New Project
                            </button>
                            <button class="btn btn-secondary w-full">
                                Generate Report
                            </button>
                            <button class="btn btn-secondary w-full">
                                View Analytics
                            </button>
                            <button class="btn btn-secondary w-full">
                                Manage Users
                            </button>
                        </div>
                    </div>
                </div>

                <!-- System Status -->
                <div class="card mt-6">
                    <div class="card-header">
                        <h2 class="text-xl font-semibold text-gray-900">System Status</h2>
                    </div>
                    <div class="card-body">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Database</span>
                                <span class="text-sm font-medium text-green-600">Online</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">API Services</span>
                                <span class="text-sm font-medium text-green-600">Operational</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Data Sync</span>
                                <span class="text-sm font-medium text-yellow-600">Syncing</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Last Backup</span>
                                <span class="text-sm font-medium text-gray-600">2 hours ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Form Example -->
        <section class="mt-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="text-xl font-semibold text-gray-900">Add New Water System</h2>
                </div>
                <div class="card-body">
                    <form class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="form-label" for="system-name">System Name</label>
                            <input type="text" id="system-name" class="form-input" placeholder="Enter system name">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="location">Location</label>
                            <select id="location" class="form-input">
                                <option>Select Province</option>
                                <option>Kigali</option>
                                <option>Eastern Province</option>
                                <option>Northern Province</option>
                                <option>Southern Province</option>
                                <option>Western Province</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="capacity">Capacity (Liters/Day)</label>
                            <input type="number" id="capacity" class="form-input" placeholder="Enter capacity">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="status">Status</label>
                            <select id="status" class="form-input">
                                <option>Planning</option>
                                <option>Under Construction</option>
                                <option>Operational</option>
                                <option>Maintenance</option>
                            </select>
                        </div>
                        
                        <div class="form-group md:col-span-2">
                            <label class="form-label" for="description">Description</label>
                            <textarea id="description" class="form-input" rows="3" placeholder="Enter description"></textarea>
                        </div>
                        
                        <div class="md:col-span-2 flex gap-4">
                            <button type="submit" class="btn btn-primary">Save System</button>
                            <button type="button" class="btn btn-secondary">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="mt-16 bg-gray-100 py-8">
        <div class="container text-center">
            <p class="text-gray-600">
                © 2024 Ministry of Infrastructure, Rwanda. All rights reserved.
            </p>
            <p class="text-sm text-gray-500 mt-2">
                Water, Sanitation, and Hygiene Management Information System
            </p>
        </div>
    </footer>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading state to buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (this.type === 'submit') {
                        e.preventDefault();
                        this.classList.add('loading');
                        setTimeout(() => {
                            this.classList.remove('loading');
                        }, 2000);
                    }
                });
            });

            // Add form validation
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.required && !this.value) {
                        this.classList.add('border-red-500');
                    } else {
                        this.classList.remove('border-red-500');
                    }
                });
            });
        });
    </script>
</body>
</html>
