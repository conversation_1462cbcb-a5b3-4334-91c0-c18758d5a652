import DynamicTable from "@/components/table";
import { View } from "react-native";

export default function Index() {
  
  const handleSubmit = async (data: any) => {
    console.log('Form submitted:', data);
    // Handle form submission
  };

  return (
    <View style={{ flex: 1, padding: 0 }}>
      <DynamicTable
        onSubmit={handleSubmit}
        showProgress={true}
        allowNavigation={true}
        submitButtonText="Submit Form"
        nextButtonText="Continue"
        backButtonText="Previous"
      />
    </View>
  );
}
