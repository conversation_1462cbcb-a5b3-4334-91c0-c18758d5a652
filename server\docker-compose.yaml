services:
  postgres:
    image: postgres:15
    container_name: postgres
    restart: always
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: wash_mis
    volumes:
      - postgres_data:/var/lib/postgresql/data

  app:
    build: .
    container_name: nestjs-app
    restart: always
    ports:
      - '3000:3000'
    depends_on:
      - postgres
    environment:
      DATABASE_URL: ****************************************/wash_mis
    command: sh -c "npx prisma migrate deploy && node dist/main"

volumes:
  postgres_data:
