generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}


model Province {
  id        Int        @id @default(autoincrement())
  name      String
  districts District[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserLocationAccess UserLocationAccess[]
}

model District {
  id         Int      @id @default(autoincrement())
  name       String
  provinceId Int
  province   Province @relation(fields: [provinceId], references: [id])
  sectors    Sector[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserLocationAccess UserLocationAccess[]
}

model Sector {
  id         Int      @id @default(autoincrement())
  name       String
  districtId Int
  district   District @relation(fields: [districtId], references: [id])
  cells      Cell[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserLocationAccess UserLocationAccess[]
}

model Cell {
  id       Int       @id @default(autoincrement())
  name     String
  sectorId Int
  sector   Sector    @relation(fields: [sectorId], references: [id])
  villages Village[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserLocationAccess UserLocationAccess[]
}

model Village {
  id     Int    @id @default(autoincrement())
  name   String
  cellId Int
  cell   Cell   @relation(fields: [cellId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  locationAccess UserLocationAccess[]

  Location Location[]
}

model User {
    id              String @id @default(cuid())
    firstName       String
    lastName        String
    email           String @unique
    telephoneNumber String @unique
    roleId          String
    role            Role   @relation(fields: [roleId], references: [id])

    locations UserLocationAccess[]
    account   Account?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    submissions Submission[]
}

model Account {
    userId String @id
    user   User   @relation(fields: [userId], references: [id])

    accountVerified   Boolean   @default(false)
    accountVerifiedAt DateTime?

    password         String?
    refreshToken     String?
    resetToken       String?
    resetTokenExpiry DateTime?

    is2FAEnabled Boolean   @default(false)
    twoFASecret  String?
    otpTempToken String?
    otpExpiresAt DateTime?

    recoveryCodes RecoveryCode[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model RecoveryCode {
    id        String  @id @default(cuid())
    accountId String
    account   Account @relation(fields: [accountId], references: [userId])

    code String
    used Boolean @default(false)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([accountId])
}

model Role {
    id         String      @id @default(cuid())
    name       String      @unique
    privileges Privilege[]

    users User[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model UserLocationAccess {
    id     String @id @default(cuid())
    userId String
    user   User   @relation(fields: [userId], references: [id])

    provinceId Int?
    province   Province? @relation(fields: [provinceId], references: [id])

    districtId Int?
    district   District? @relation(fields: [districtId], references: [id])

    sectorId Int?
    sector   Sector? @relation(fields: [sectorId], references: [id])

    cellId Int?
    cell   Cell? @relation(fields: [cellId], references: [id])

    villageId Int?
    village   Village? @relation(fields: [villageId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
    @@index([districtId])
    @@index([sectorId])
    @@index([villageId])
}

enum Privilege {
    USER_MANAGEMENT
    DATA_COLLECTION
    //system modules
}

enum SettlementType {
  RURAL
  URBAN
}

model Location {
  id        String   @id @default(uuid())
  villageId Int
  village   Village  @relation(fields: [villageId], references: [id])

  latitude  Float?
  longitude Float?

  settlementType SettlementType @default(RURAL)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  houseHolds HouseHold[]
  schools School[]
  healthFacilities HealthFacility[]
  markets Market[]
  busStations BusStation[]
  wasteCollectionCompanies WasteCollectionCompany[]
  wasteDisposalCompanies WasteDisposalCompany[]
  wasteRecoveryCompanies WasteRecoveryCompany[]
}

model HouseHold {
  id        String   @id @default(uuid())
  location  Location @relation(fields: [locationId], references: [id])
  locationId String
  number Int @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Submission Submission[]
}

model School {
  id        String   @id @default(uuid())
  location  Location @relation(fields: [locationId], references: [id])
  locationId String
  name String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Submission Submission[]
}

model HealthFacility {
  id        String   @id @default(uuid())
  location  Location @relation(fields: [locationId], references: [id])
  locationId String
  name String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Submission Submission[]
}

model Market {
  id        String   @id @default(uuid())
  location  Location @relation(fields: [locationId], references: [id])
  locationId String
  name String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Submission Submission[]
}

model BusStation{
  id        String   @id @default(uuid())
  location  Location @relation(fields: [locationId], references: [id])
  locationId String
  name String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Submission Submission[]
}

model WasteCollectionCompany {
  id        String   @id @default(uuid())
  location  Location @relation(fields: [locationId], references: [id])
  locationId String
  name String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Submission Submission[]
}

model WasteRecoveryCompany {
  id        String   @id @default(uuid())
  location  Location @relation(fields: [locationId], references: [id])
  locationId String
  name String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Submission Submission[]
}

model WasteDisposalCompany {
  id        String   @id @default(uuid())
  location  Location @relation(fields: [locationId], references: [id])
  locationId String
  name String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Submission Submission[]
}

enum FacilityType {
  HOUSEHOLD
  SCHOOL
  HEALTH_FACILITY
  MARKET
  BUS_STATION
  WASTE_COLLECTION_COMPANY
  WASTE_RECOVERY_COMPANY
  WASTE_DISPOSAL_COMPANY
}

model Submission {
  id               String        @id @default(uuid())
  facilityType     FacilityType
  submittedById    String
  submittedBy      User          @relation(fields: [submittedById], references: [id])
  submittedAt      DateTime      @default(now())

  houseHoldId      String?
  household        HouseHold?       @relation(fields: [houseHoldId], references: [id])

  schoolId         String?
  school           School?          @relation(fields: [schoolId], references: [id])

  healthFacilityId String?
  healthFacility   HealthFacility?  @relation(fields: [healthFacilityId], references: [id])

  marketId         String?
  market           Market?          @relation(fields: [marketId], references: [id])

  busStationId     String?
  busStation       BusStation?      @relation(fields: [busStationId], references: [id])

  wasteCollectionCompanyId String?
  wasteCollectionCompany WasteCollectionCompany? @relation(fields: [wasteCollectionCompanyId], references: [id])

  wasteRecoveryCompanyId String?
  wasteRecoveryCompany   WasteRecoveryCompany?   @relation(fields: [wasteRecoveryCompanyId], references: [id])

  wasteDisposalCompanyId String?
  wasteDisposalCompany   WasteDisposalCompany?   @relation(fields: [wasteDisposalCompanyId], references: [id])
}