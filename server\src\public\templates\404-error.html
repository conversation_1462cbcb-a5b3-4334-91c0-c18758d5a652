<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - WASH MIS</title>
    <link rel="stylesheet" href="../styles/wash-mis-design-system.css">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💧</text></svg>">
    <style>
        .error-container {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-4);
        }
        
        .error-card {
            background: white;
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            width: 100%;
            max-width: 600px;
            text-align: center;
            border: 1px solid var(--gray-200);
        }
        
        .error-header {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            color: white;
            padding: var(--space-8) var(--space-6);
            position: relative;
            overflow: hidden;
        }
        
        .error-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .error-content {
            position: relative;
            z-index: 1;
        }
        
        .error-body {
            padding: var(--space-12) var(--space-6);
        }
        
        .error-footer {
            background-color: var(--gray-50);
            padding: var(--space-6);
            border-top: 1px solid var(--gray-200);
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: var(--font-extrabold);
            color: var(--primary-600);
            margin-bottom: var(--space-4);
            line-height: 1;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .error-title {
            font-size: var(--text-3xl);
            font-weight: var(--font-bold);
            color: var(--gray-900);
            margin-bottom: var(--space-4);
        }
        
        .error-description {
            font-size: var(--text-lg);
            color: var(--gray-600);
            margin-bottom: var(--space-8);
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
            align-items: center;
        }
        
        .search-box {
            width: 100%;
            max-width: 400px;
            margin-bottom: var(--space-6);
        }
        
        .search-input {
            width: 100%;
            padding: var(--space-4);
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-xl);
            font-size: var(--text-base);
            transition: all var(--transition-fast);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .helpful-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            width: 100%;
            margin-top: var(--space-8);
        }
        
        .link-card {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            text-decoration: none;
            color: var(--gray-700);
            transition: all var(--transition-fast);
        }
        
        .link-card:hover {
            background: white;
            border-color: var(--primary-300);
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        
        .link-icon {
            font-size: var(--text-2xl);
            margin-bottom: var(--space-2);
        }
        
        .link-title {
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-1);
        }
        
        .link-description {
            font-size: var(--text-sm);
            color: var(--gray-500);
        }
        
        .breadcrumb {
            background: white;
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid var(--gray-200);
            font-size: var(--text-sm);
            color: var(--gray-600);
        }
        
        .breadcrumb a {
            color: var(--primary-600);
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .error-container {
                padding: var(--space-2);
            }
            
            .error-code {
                font-size: 4rem;
            }
            
            .error-title {
                font-size: var(--text-2xl);
            }
            
            .error-description {
                font-size: var(--text-base);
            }
            
            .error-actions {
                flex-direction: column;
            }
            
            .helpful-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-card">
            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="/">WASH MIS</a> / <span>Error 404</span>
            </div>
            
            <!-- Header -->
            <div class="error-header">
                <div class="error-content">
                    <div style="font-size: 3rem; margin-bottom: var(--space-2);">💧</div>
                    <h1 style="font-size: var(--text-xl); font-weight: var(--font-semibold); margin: 0;">
                        WASH MIS
                    </h1>
                    <p style="font-size: var(--text-sm); opacity: 0.9; margin: var(--space-1) 0 0 0;">
                        Ministry of Infrastructure - Rwanda
                    </p>
                </div>
            </div>
            
            <!-- Body -->
            <div class="error-body">
                <div class="error-code">404</div>
                <h2 class="error-title">Page Not Found</h2>
                <p class="error-description">
                    The page you're looking for doesn't exist or has been moved. 
                    Let's help you find what you need in the WASH Management Information System.
                </p>
                
                <!-- Search Box -->
                <div class="search-box">
                    <input 
                        type="text" 
                        class="search-input" 
                        placeholder="Search for water systems, reports, or projects..."
                        id="searchInput"
                    >
                </div>
                
                <!-- Action Buttons -->
                <div class="error-actions">
                    <button class="btn btn-primary btn-lg" onclick="goHome()">
                        🏠 Return to Dashboard
                    </button>
                    <button class="btn btn-secondary" onclick="goBack()">
                        ← Go Back
                    </button>
                </div>
                
                <!-- Helpful Links -->
                <div class="helpful-links">
                    <a href="/dashboard" class="link-card">
                        <div class="link-icon">📊</div>
                        <div class="link-title">Dashboard</div>
                        <div class="link-description">View system overview and statistics</div>
                    </a>
                    
                    <a href="/water-systems" class="link-card">
                        <div class="link-icon">🚰</div>
                        <div class="link-title">Water Systems</div>
                        <div class="link-description">Manage water infrastructure</div>
                    </a>
                    
                    <a href="/sanitation" class="link-card">
                        <div class="link-icon">🚽</div>
                        <div class="link-title">Sanitation</div>
                        <div class="link-description">Track sanitation facilities</div>
                    </a>
                    
                    <a href="/reports" class="link-card">
                        <div class="link-icon">📋</div>
                        <div class="link-title">Reports</div>
                        <div class="link-description">Generate and view reports</div>
                    </a>
                    
                    <a href="/projects" class="link-card">
                        <div class="link-icon">🏗️</div>
                        <div class="link-title">Projects</div>
                        <div class="link-description">Monitor ongoing projects</div>
                    </a>
                    
                    <a href="/help" class="link-card">
                        <div class="link-icon">❓</div>
                        <div class="link-title">Help Center</div>
                        <div class="link-description">Get support and documentation</div>
                    </a>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="error-footer">
                <p class="text-sm text-gray-600">
                    Need technical assistance? Contact our support team at 
                    <a href="mailto:<EMAIL>" class="text-primary-600"><EMAIL></a>
                </p>
                <p class="text-xs text-gray-500 mt-2">
                    Error Code: 404 | Time: <span id="currentTime"></span>
                </p>
            </div>
        </div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString();
        }
        updateTime();

        // Navigation functions
        function goHome() {
            window.location.href = '/dashboard';
        }

        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                goHome();
            }
        }

        // Search functionality
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    // In a real application, this would redirect to search results
                    alert(`Searching for: "${query}"\n\nThis would redirect to search results in a real application.`);
                }
            }
        });

        // Add some interactive feedback
        document.querySelectorAll('.link-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-2px)';
            });
        });

        // Track 404 errors (in a real app, this would send to analytics)
        console.log('404 Error tracked:', {
            url: window.location.href,
            referrer: document.referrer,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        });
    </script>
</body>
</html>
