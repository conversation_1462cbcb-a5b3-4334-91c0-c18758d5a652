# Database
DATABASE_URL="postgresql://username:password@localhost:5432/wash_mis_db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-here"
JWT_REFRESH_EXPIRES_IN="7d"

# Application Configuration
APP_NAME="WASH MIS"
APP_FULL_NAME="Water, Sanitation, and Hygiene Management Information System"
PORT=8080
FE_URL="http://localhost:3000"
FRONTEND_URL="http://localhost:3000"

# Email Configuration (for production, use real SMTP settings)
SMTP_HOST="localhost"
SMTP_PORT=1025
SMTP_USER=""
SMTP_PASS=""
SMTP_SECURE=false

# Support Information
SUPPORT_EMAIL="<EMAIL>"
SUPPORT_PHONE="+*********** 456"
LOGO_URL="https://your-domain.com/logo.png"

# Social Media Links (optional)
TWITTER_URL="https://twitter.com/MinInfra_Rwanda"
FACEBOOK_URL="https://facebook.com/MinInfra.Rwanda"
LINKEDIN_URL="https://linkedin.com/company/mininfra-rwanda"