# 💧 WASH MIS - Water, Sanitation, and Hygiene Management Information System

A modern, comprehensive management information system for water, sanitation, and hygiene infrastructure developed for the Ministry of Infrastructure, Republic of Rwanda.

## 🌟 Features

### 🔐 Authentication & Security
- **JWT-based authentication** with refresh tokens
- **Two-Factor Authentication (2FA)** using TOTP
- **Role-based access control** with privilege management
- **Location-based access restrictions**
- **Mobile user restrictions** for web endpoints
- **Secure password reset** with email verification
- **Account verification** via email

### 👥 User Management
- **Privilege-based user creation** (USER_MANAGEMENT required)
- **No public registration** - admin-only user creation
- **Comprehensive user profiles** with location access
- **Email notifications** for account setup and security

### 🎨 Modern UI/UX
- **Contemporary design system** with blue/white theme
- **Responsive design** for desktop, tablet, and mobile
- **Accessibility compliant** (WCAG 2.1)
- **Modern CSS framework** with design tokens
- **Interactive components** with loading states
- **Professional government branding**

### 📧 Email System
- **Responsive email templates** with modern design
- **Handlebars templating** for dynamic content
- **Multi-purpose templates**: user creation, password reset, 2FA setup, verification
- **Professional branding** for Ministry of Infrastructure

## 🏗️ Architecture

### Technology Stack
- **Backend**: NestJS with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with Passport.js
- **Email**: Nodemailer with Handlebars templates
- **2FA**: Speakeasy with QR code generation
- **API Documentation**: Swagger/OpenAPI
- **Package Manager**: pnpm

### Project Structure
```
src/
├── auth/                 # Authentication module
│   ├── dto/             # Data transfer objects
│   ├── auth.controller.ts
│   ├── auth.service.ts
│   └── auth.module.ts
├── users/               # User management module
│   ├── dto/
│   ├── users.controller.ts
│   ├── users.service.ts
│   └── users.module.ts
├── common/              # Shared components
│   ├── guards/          # Authentication guards
│   └── decorators/      # Custom decorators
├── email/               # Email service
│   ├── templates/       # Handlebars templates
│   ├── email.service.ts
│   └── email.module.ts
├── prisma/              # Database service
└── public/              # Static assets
    ├── styles/          # CSS design system
    └── templates/       # HTML templates
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL 12+
- pnpm package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd wash-mis-backend
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database setup**
   ```bash
   # Generate Prisma client
   npx prisma generate

   # Run migrations
   npx prisma db push

   # Seed initial data
   pnpm run seed
   ```

5. **Start development server**
   ```bash
   pnpm run dev
   ```

The API will be available at `http://localhost:8080` with documentation at `http://localhost:8080/api-docs`.

### Environment Variables

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/wash_mis_db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-here"
JWT_REFRESH_EXPIRES_IN="7d"

# Application
APP_NAME="WASH MIS"
APP_FULL_NAME="Water, Sanitation, and Hygiene Management Information System"
FRONTEND_URL="http://localhost:3000"

# Email Support
SUPPORT_EMAIL="<EMAIL>"
SUPPORT_PHONE="+250 788 123 456"
```

## 📱 API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/verify-2fa` - Verify 2FA code
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/request-password-reset` - Request password reset
- `POST /api/v1/auth/reset-password` - Reset password
- `GET /api/v1/auth/2fa/setup` - Setup 2FA
- `POST /api/v1/auth/2fa/enable` - Enable 2FA

### User Management
- `POST /api/v1/users` - Create user (USER_MANAGEMENT privilege required)
- `GET /api/v1/users` - List users with pagination
- `GET /api/v1/users/me` - Get current user profile
- `GET /api/v1/users/:id` - Get user by ID
- `PATCH /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Delete user

## 🎨 Design System

The WASH MIS includes a comprehensive design system with:

### Color Palette
- **Primary**: Infrastructure blue (#3b82f6 to #1e40af)
- **Neutral**: Gray scale (#f8fafc to #0f172a)
- **Semantic**: Success, warning, and error colors

### Typography
- **Font**: Inter (modern, readable)
- **Scales**: xs (12px) to 4xl (36px)
- **Weights**: Light to extrabold

### Components
- **Cards**: Modern with shadows and hover effects
- **Buttons**: Multiple variants with loading states
- **Forms**: Accessible with validation
- **Navigation**: Responsive with modern styling

### Usage
```html
<!-- Include the design system -->
<link rel="stylesheet" href="/styles/wash-mis-design-system.css">

<!-- Use component classes -->
<div class="card">
  <div class="card-header">
    <h2 class="text-xl font-semibold">Title</h2>
  </div>
  <div class="card-body">
    <button class="btn btn-primary">Action</button>
  </div>
</div>
```

## 🔒 Security Features

### Authentication Flow
1. User enters credentials
2. System validates and checks 2FA status
3. If 2FA enabled, temporary token issued
4. User provides 2FA code or recovery code
5. JWT tokens issued upon successful verification

### Access Control
- **AuthGuard**: Validates JWT tokens
- **PrivilegeGuard**: Checks user privileges
- **MobileGuard**: Restricts mobile-only users

### Password Security
- Minimum 8 characters with complexity requirements
- Bcrypt hashing with salt rounds
- Secure reset tokens with expiration

## 📧 Email Templates

Modern, responsive email templates with:
- **Professional branding** for Ministry of Infrastructure
- **Blue and white color scheme**
- **Mobile-responsive design**
- **Accessibility features**
- **Security notices and instructions**

Templates include:
- User creation and setup
- Password reset
- 2FA setup instructions
- Account verification

## 🧪 Testing

```bash
# Unit tests
pnpm run test

# E2E tests
pnpm run test:e2e

# Test coverage
pnpm run test:cov
```

## 📚 Documentation

- **API Documentation**: Available at `/api-docs` when running
- **Authentication Guide**: See `AUTHENTICATION.md`
- **Design System**: See `/public/styles/wash-mis-design-system.css`
- **Email Templates**: See `/src/email/templates/`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the terms specified by the Ministry of Infrastructure, Republic of Rwanda.

## 🆘 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Phone**: +250 788 123 456

---

**Ministry of Infrastructure, Republic of Rwanda**
*Building sustainable water, sanitation, and hygiene infrastructure for all Rwandans*
