import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: 'user_123',
  })
  id: string;

  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>',
  })
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
  })
  lastName: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User telephone number',
    example: '+************',
  })
  telephoneNumber: string;

  @ApiProperty({
    description: 'User role information',
  })
  role: {
    id: string;
    name: string;
    privileges: string[];
  };

  @ApiProperty({
    description: 'Account verification status',
    example: true,
  })
  accountVerified: boolean;

  @ApiProperty({
    description: '2FA enabled status',
    example: false,
  })
  is2FAEnabled: boolean;

  @ApiProperty({
    description: 'User location access',
    type: [Object],
  })
  locations: {
    id: string;
    province?: { id: number; name: string };
    district?: { id: number; name: string };
    sector?: { id: number; name: string };
    cell?: { id: number; name: string };
    village?: { id: number; name: string };
  }[];

  @ApiProperty({
    description: 'User creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'User last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class UsersListResponseDto {
  @ApiProperty({
    description: 'List of users',
    type: [UserResponseDto],
  })
  users: UserResponseDto[];

  @ApiProperty({
    description: 'Total number of users',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Current page',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 10,
  })
  totalPages: number;
}
