# WASH MIS Mobile App

React Native mobile application built with Expo for the Water, Sanitation, and Hygiene Management Information System.

## Running Instructions

### Local Development

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start the development server**
   ```bash
   npm start
   # or
   npx expo start
   ```

3. **Run on specific platforms**
   ```bash
   npm run android    # Android emulator
   npm run ios        # iOS simulator
   npm run web        # Web browser
   ```

### Docker (Optional)

1. **Build and run with Docker**
   ```bash
   docker build -t wash-mis-mobile .
   docker run -p 8081:8081 -p 19000:19000 -p 19001:19001 -p 19002:19002 wash-mis-mobile
   ```

2. **Or use Docker Compose**
   ```bash
   docker-compose up
   ```

## Access Points

- **Expo DevTools**: http://localhost:19002
- **Metro Bundler**: http://localhost:8081
- **Web Version**: http://localhost:8081 (when running web)

## Development

- Edit files in the `app/` directory (uses file-based routing)
- Use Expo Go app on your phone to scan QR code for testing
- Hot reload enabled for fast development
