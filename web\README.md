# WASH MIS Web Application

Angular frontend application for the Water, Sanitation, and Hygiene Management Information System.

## Running Instructions

### Local Development

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start development server**
   ```bash
   npm start
   # or
   ng serve
   ```

3. **Build for production**
   ```bash
   npm run build
   # or
   ng build
   ```

### Docker (Optional)

1. **Build and run with <PERSON>er**
   ```bash
   docker build -t wash-mis-web .
   docker run -p 4200:4200 wash-mis-web
   ```

2. **Or use Docker Compose**
   ```bash
   docker-compose up
   ```

## Access Points

- **Development**: http://localhost:4200
- **Production**: Served via Nginx on port 4200 (Docker)

## Development

- **Generate components**: `ng generate component component-name`
- **Run tests**: `npm test` or `ng test`
- **Hot reload** enabled for fast development
- **Build artifacts** stored in `dist/` directory
